/* src/Components/landing.css */
:root {
  --primary-color: #4a90e2;
  --secondary-color: #333;
  --background-color: #f4f7f6;
  --text-color: #333;
  --white: #ffffff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-color);
}

.landing-page {
  width: 100%;
}

.hero {
  background: linear-gradient(135deg, rgba(74,144,226,0.9), rgba(0,0,0,0.7)),
              url('https://source.unsplash.com/random/1600x900?technology');
  background-size: cover;
  background-position: center;
  color: var(--white);
  text-align: center;
  padding: 150px 20px;
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 20px;
  font-weight: bold;
}

.hero-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}

.btn {
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.btn.primary {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn.secondary {
  background-color: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.features {
  padding: 80px 20px;
  // src/Components/landing.jsx
import React, { useState } from 'react';
import './landing.css';

const Landing = () => {
  const [activeFeature, setActiveFeature] = useState(null);

  const features = [
    {
      id: 1,
      title: 'Innovative Design',
      icon: '🚀',
      description: 'Cutting-edge solutions that push boundaries',
      details: 'Our innovative approach transforms challenges into opportunities, delivering unique and impactful solutions.'
    },
    {
      id: 2,
      title: 'Expert Consulting',
      icon: '💡',
      description: 'Strategic guidance for your business',
      details: 'Leverage our expertise to optimize your processes, improve efficiency, and drive sustainable growth.'
    },
    {
      id: 3,
      title: 'Advanced Technology',
      icon: '🌐',
      description: 'Leveraging the latest technological trends',
      details: 'Stay ahead of the curve with our state-of-the-art technological solutions and forward-thinking strategies.'
    }
  ];

  return (
    <div className="landing-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-overlay">
          <div className="hero-content">
            <h1>Transforming Ideas into Reality</h1>
            <p>Empowering businesses through innovative solutions and strategic thinking</p>
            <div className="hero-cta">
              <button className="btn primary">Get Started</button>
              <button className="btn secondary">Learn More</button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="features-header">
          <h2>Our Key Capabilities</h2>
          <p>Comprehensive solutions designed to elevate your business</p>
        </div>
        <div className="features-grid">
          {features.map((feature) => (
            <div 
              key={feature.id} 
              className={`feature-card ${activeFeature === feature.id ? 'active' : ''}`}
              onMouseEnter={() => setActiveFeature(feature.id)}
              onMouseLeave={() => setActiveFeature(null)}
            >
              <div className="feature-icon">{feature.icon}</div>
              <h3>{feature.title}</h3>
              <p>{feature.description}</p>
              {activeFeature === feature.id && (
                <div className="feature-hover-details">
                  <p>{feature.details}</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </section>

      {/* About Section */}
      <section className="about">
        <div className="about-content">
          <div className="about-text">
            <h2>Who We Are</h2>
            <p>
              We are a dynamic team of innovators, strategists, and problem-solvers 
              committed to driving transformation. Our passion lies in creating 
              tailored solutions that address unique business challenges and unlock 
              new possibilities.
            </p>
            <ul className="about-highlights">
              <li>✓ Creative Problem Solving</li>
              <li>✓ Customer-Centric Approach</li>
              <li>✓ Continuous Innovation</li>
            </ul>
            <button className="btn primary">Our Story</button>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="testimonials">
        <div className="testimonials-header">
          <h2>What Our Clients Say</h2>
        </div>
        <div className="testimonial-cards">
          <div className="testimonial-card">
            <p>"Their innovative solutions transformed our business strategy completely."</p>
            <div className="client-info">
              <span className="client-name">- Sarah Johnson</span>
              <span className="client-company">Tech Innovations Inc.</span>
            </div>
          </div>
          <div className="testimonial-card">
            <p>"Exceptional consulting that delivered real, measurable results."</p>
            <div className="client-info">
              <span className="client-name">- Michael Chen</span>
              <span className="client-company">Global Enterprises</span>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="contact">
        <div className="contact-content">
          <h2>Ready to Transform Your Business?</h2>
          <p>Let's discuss how we can help you achieve your goals</p>
          <form className="contact-form">
            <input type="text" placeholder="Your Name" required />
            <input type="email" placeholder="Your Email" required />
            <textarea placeholder="Tell us about your project" required></textarea>
            <button type="submit" className="btn submit">Send Inquiry</button>
          </form>
        </div>
      </section>

      {/* Footer */}
      <footer className="footer">
        <div className="footer-content">
          <div className="footer-sections">
            <div className="footer-section">
              <h4>Quick Links</h4>
              <a href="#">Home</a>
              <a href="#">Services</a>
              <a href="#">About Us</a>
            </div>
            <div className="footer-section">
              <h4>Connect</h4>
              <a href="#">LinkedIn</a>
              <a href="#">Twitter</a>
              <a href="#">Email</a>
            </div>
          </div>
          <div className="footer-bottom">
            <p>© 2025 Innovative Solutions. All Rights Reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;